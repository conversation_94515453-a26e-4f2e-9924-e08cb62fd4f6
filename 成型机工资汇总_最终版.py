import pandas as pd
import os
import numpy as np
import re
import openpyxl
from openpyxl.utils.cell import get_column_letter
from openpyxl.styles import Alignment, Border, Side, Font, PatternFill
import sys

# 设置文件路径
if len(sys.argv) > 1:
    excel_file = sys.argv[1]
else:
    excel_file = "2025.5月份成型机每日计件带重量  .xlsx"  # 默认文件名

desktop_path = os.path.expanduser("~/Desktop")
file_path = os.path.join(desktop_path, excel_file)

# 检查文件是否存在
if not os.path.exists(file_path):
    file_path = "2025.5月份成型机每日计件带重量.xlsx"  # 尝试当前目录

# 获取日期页签和所有人名
wb = openpyxl.load_workbook(file_path, data_only=True)  # data_only=True会读取公式的计算结果
# 支持任意月份的"数字.数字"格式sheet
all_sheets = [s for s in wb.sheetnames if re.match(r'\d+\.\d+', s)]

# 自定义日期排序函数 - 按照数值大小而非字符串排序
def sort_date_key(sheet_name):
    if not sheet_name.startswith('5.'):
        return 9999  # 非日期页签放到最后
    try:
        # 提取日期部分并转换为浮点数
        return float(sheet_name[2:])
    except:
        return 9999

# 按照正确的日期顺序排序页签
sheets = sorted(all_sheets, key=sort_date_key)
print(f"排序后的日期页签顺序: {sheets}")

# 获取所有人名（排除"合计"）
all_names = set()
for sheet in sheets:
    ws = wb[sheet]
    # 查找姓名列的索引
    name_col = None
    for col in range(1, ws.max_column + 1):
        if ws.cell(row=1, column=col).value == '姓名':
            name_col = col
            break
    
    if name_col:
        # 获取所有非空姓名
        for row in range(2, ws.max_row + 1):
            name = ws.cell(row=row, column=name_col).value
            if name and str(name).strip() and name != '合计':
                all_names.add(name)

all_names = sorted(list(all_names))
print(f"共找到 {len(all_names)} 个姓名: {all_names}")

# 创建最终的汇总表
summary_table = {}
for name in all_names:
    summary_table[name] = {'合计': {}, '工时': {}, '平均小时工资': {}, '备注': {}}
    for sheet in sheets:
        summary_table[name]['合计'][sheet] = 0
        summary_table[name]['工时'][sheet] = 0
        summary_table[name]['平均小时工资'][sheet] = 0
        summary_table[name]['备注'][sheet] = ''

# 填充数据
for sheet in sheets:
    print(f"处理 {sheet} 页签...")
    ws = wb[sheet]
    
    # 查找列索引
    col_indices = {}
    for col in range(1, ws.max_column + 1):
        col_header = ws.cell(row=1, column=col).value
        if col_header in ['姓名', '合计', '工时', '备注']:
            col_indices[col_header] = col
    
    # 主要人名数据提取
    for row in range(2, ws.max_row + 1):
        name = ws.cell(row=row, column=col_indices.get('姓名', 0)).value
        
        if name in all_names:
            # 获取合计和工时的值（计算后的实际值）
            total = ws.cell(row=row, column=col_indices.get('合计', 0)).value
            if total and isinstance(total, (int, float)):
                summary_table[name]['合计'][sheet] = total
            
            work_hours = ws.cell(row=row, column=col_indices.get('工时', 0)).value
            if work_hours and isinstance(work_hours, (int, float)):
                summary_table[name]['工时'][sheet] = work_hours
                
                # 计算平均小时工资
                if work_hours > 0:
                    avg_hourly_wage = total / work_hours
                    summary_table[name]['平均小时工资'][sheet] = round(avg_hourly_wage, 1)
            
            # 查找该人名下所有行的备注
            start_row = row
            end_row = row
            # 找到下一个人名或表末尾
            for r in range(row + 1, ws.max_row + 1):
                if ws.cell(row=r, column=col_indices.get('姓名', 0)).value:
                    end_row = r - 1
                    break
                end_row = r
            
            # 在该人名的所有行中查找备注
            remarks = []
            for r in range(start_row, end_row + 1):
                remark = ws.cell(row=r, column=col_indices.get('备注', 0)).value
                if remark and str(remark).strip():
                    remarks.append(str(remark).strip())
            
            if remarks:
                summary_table[name]['备注'][sheet] = ', '.join(remarks)

# 计算汇总列数据
for name in all_names:
    # 合计和工时的汇总
    total_sum = sum(summary_table[name]['合计'].values())
    hours_sum = sum(summary_table[name]['工时'].values())
    
    # 计算平均小时工资
    avg_hourly_wage_sum = 0
    if hours_sum > 0:
        avg_hourly_wage_sum = round(total_sum / hours_sum, 1)
    
    # 统计请假次数
    leave_count = 0
    for sheet in sheets:
        remarks = summary_table[name]['备注'][sheet]
        if '请假' in remarks:
            leave_count += 1
    
    # 将汇总数据添加到summary_table
    summary_table[name]['合计']['汇总'] = total_sum
    summary_table[name]['工时']['汇总'] = hours_sum
    summary_table[name]['平均小时工资']['汇总'] = avg_hourly_wage_sum
    summary_table[name]['备注']['汇总'] = leave_count if leave_count > 0 else ''

# 创建DataFrame以便转为Excel格式
columns = ['姓名', '类型'] + sheets + ['汇总']

# 准备行数据
rows = []
for name in all_names:
    # 合计行
    total_row = [name, '合计']
    for sheet in sheets + ['汇总']:
        total_row.append(summary_table[name]['合计'].get(sheet, 0))
    rows.append(total_row)
    
    # 工时行
    hours_row = ['', '工时']
    for sheet in sheets + ['汇总']:
        hours_row.append(summary_table[name]['工时'].get(sheet, 0))
    rows.append(hours_row)
    
    # 平均小时工资行
    avg_wage_row = ['', '平均小时工资']
    for sheet in sheets + ['汇总']:
        avg_wage_row.append(summary_table[name]['平均小时工资'].get(sheet, 0))
    rows.append(avg_wage_row)
    
    # 备注行
    remarks_row = ['', '备注']
    for sheet in sheets + ['汇总']:
        remarks_row.append(summary_table[name]['备注'].get(sheet, ''))
    rows.append(remarks_row)
    
    # 添加两行空行（如果不是最后一个人）
    if name != all_names[-1]:
        empty_row1 = [''] * len(columns)
        empty_row2 = [''] * len(columns)
        rows.append(empty_row1)
        rows.append(empty_row2)

# 创建DataFrame
df = pd.DataFrame(rows, columns=columns)

# 保存到Excel
output_file = os.path.join(desktop_path, os.path.splitext(excel_file)[0] + '_汇总版.xlsx')
with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
    df.to_excel(writer, index=False, sheet_name='汇总表')
    
    # 设置列宽
    worksheet = writer.sheets['汇总表']
    worksheet.column_dimensions['A'].width = 15  # 姓名列
    worksheet.column_dimensions['B'].width = 15  # 类型列
    
    # 日期列
    for i, _ in enumerate(sheets + ['汇总'], start=3):
        col_letter = get_column_letter(i)
        worksheet.column_dimensions[col_letter].width = 10
    
    # 定义样式
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    center_alignment = Alignment(horizontal='center', vertical='center')
    bold_font = Font(bold=True)
    header_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    
    # 设置表头样式
    for cell in worksheet[1]:
        cell.font = bold_font
        cell.border = thin_border
        cell.alignment = center_alignment
        cell.fill = header_fill
    
    # 合并姓名单元格并设置样式
    current_row = 2  # 从第2行开始（第1行是标题）
    for i, name in enumerate(all_names):
        # 每个人占4行，合并姓名单元格
        worksheet.merge_cells(f'A{current_row}:A{current_row+3}')
        merged_cell = worksheet[f'A{current_row}']
        merged_cell.alignment = Alignment(horizontal='center', vertical='center')
        merged_cell.font = bold_font
        
        # 为每个人的所有单元格添加边框和居中对齐
        for row in range(current_row, current_row + 4):
            for col in range(1, len(columns) + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.border = thin_border
                if col > 1:  # 除了姓名列之外的所有单元格
                    cell.alignment = center_alignment
        
        # 计算下一个人的起始行（加上4行数据和2行空行）
        if i < len(all_names) - 1:  # 如果不是最后一个人
            current_row += 6  # 4行数据 + 2行空行
        else:
            current_row += 4  # 最后一个人不需要加空行

    # 设置冻结窗格
    worksheet.freeze_panes = 'C2'

print(f"汇总完成！数据已保存到: {output_file}")

# 关闭工作簿
wb.close() 