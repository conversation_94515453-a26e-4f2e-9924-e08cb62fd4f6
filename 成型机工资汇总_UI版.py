import pandas as pd
import os
import numpy as np
import re
import openpyxl
import sys
import threading
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from openpyxl.utils.cell import get_column_letter
from openpyxl.styles import Alignment, Border, Side, Font, PatternFill

# 主函数：处理Excel文件并生成汇总
def process_excel(excel_file, progress_var=None, status_var=None):
    try:
        if status_var:
            status_var.set("正在处理Excel文件...")
        
        desktop_path = os.path.expanduser("~/Desktop")
        file_path = os.path.join(desktop_path, excel_file)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            file_path = excel_file  # 尝试直接使用完整路径

        if not os.path.exists(file_path):
            if status_var:
                status_var.set(f"错误：找不到文件 {excel_file}")
            return False

        # 获取日期页签和所有人名
        wb = openpyxl.load_workbook(file_path, data_only=True)  # data_only=True会读取公式的计算结果
        # 支持任意月份的"数字.数字"格式sheet
        all_sheets = [s for s in wb.sheetnames if re.match(r'\d+\.\d+', s)]

        # 自定义日期排序函数 - 按照数值大小而非字符串排序
        def sort_date_key(sheet_name):
            try:
                # 提取日期部分并转换为浮点数
                parts = sheet_name.split('.')
                if len(parts) >= 2:
                    return float(parts[0]) * 100 + float(parts[1])
                return 9999
            except:
                return 9999

        # 按照正确的日期顺序排序页签
        sheets = sorted(all_sheets, key=sort_date_key)
        if status_var:
            status_var.set(f"找到 {len(sheets)} 个日期页签")
        
        # 获取所有人名（排除"合计"）
        all_names = set()
        for sheet in sheets:
            ws = wb[sheet]
            # 查找姓名列的索引
            name_col = None
            for col in range(1, ws.max_column + 1):
                if ws.cell(row=1, column=col).value == '姓名':
                    name_col = col
                    break
            
            if name_col:
                # 获取所有非空姓名
                for row in range(2, ws.max_row + 1):
                    name = ws.cell(row=row, column=name_col).value
                    if name and str(name).strip() and name != '合计':
                        all_names.add(name)

        all_names = sorted(list(all_names))
        if status_var:
            status_var.set(f"找到 {len(all_names)} 个姓名")
        
        # 创建最终的汇总表
        summary_table = {}
        for name in all_names:
            summary_table[name] = {'合计': {}, '工时': {}, '平均小时工资': {}, '备注': {}}
            for sheet in sheets:
                summary_table[name]['合计'][sheet] = 0
                summary_table[name]['工时'][sheet] = 0
                summary_table[name]['平均小时工资'][sheet] = 0
                summary_table[name]['备注'][sheet] = ''

        # 填充数据
        total_sheets = len(sheets)
        for i, sheet in enumerate(sheets):
            if status_var:
                status_var.set(f"处理 {sheet} 页签... ({i+1}/{total_sheets})")
            if progress_var:
                progress_var.set((i + 1) / total_sheets * 100)
            
            ws = wb[sheet]
            
            # 查找列索引
            col_indices = {}
            for col in range(1, ws.max_column + 1):
                col_header = ws.cell(row=1, column=col).value
                if col_header in ['姓名', '合计', '工时', '备注']:
                    col_indices[col_header] = col
            
            # 主要人名数据提取
            for row in range(2, ws.max_row + 1):
                name = ws.cell(row=row, column=col_indices.get('姓名', 0)).value
                
                if name in all_names:
                    # 获取合计和工时的值（计算后的实际值）
                    total = ws.cell(row=row, column=col_indices.get('合计', 0)).value
                    if total and isinstance(total, (int, float)):
                        summary_table[name]['合计'][sheet] = total
                    
                    work_hours = ws.cell(row=row, column=col_indices.get('工时', 0)).value
                    if work_hours and isinstance(work_hours, (int, float)):
                        summary_table[name]['工时'][sheet] = work_hours
                        
                        # 计算平均小时工资
                        if work_hours > 0:
                            avg_hourly_wage = total / work_hours
                            summary_table[name]['平均小时工资'][sheet] = round(avg_hourly_wage, 1)
                    
                    # 查找该人名下所有行的备注
                    start_row = row
                    end_row = row
                    # 找到下一个人名或表末尾
                    for r in range(row + 1, ws.max_row + 1):
                        if ws.cell(row=r, column=col_indices.get('姓名', 0)).value:
                            end_row = r - 1
                            break
                        end_row = r
                    
                    # 在该人名的所有行中查找备注
                    remarks = []
                    for r in range(start_row, end_row + 1):
                        remark = ws.cell(row=r, column=col_indices.get('备注', 0)).value
                        if remark and str(remark).strip():
                            remarks.append(str(remark).strip())
                    
                    if remarks:
                        summary_table[name]['备注'][sheet] = ', '.join(remarks)

        if status_var:
            status_var.set("计算汇总数据...")
        
        # 计算汇总列数据
        for name in all_names:
            # 合计和工时的汇总
            total_sum = sum(summary_table[name]['合计'].values())
            hours_sum = sum(summary_table[name]['工时'].values())
            
            # 计算平均小时工资
            avg_hourly_wage_sum = 0
            if hours_sum > 0:
                avg_hourly_wage_sum = round(total_sum / hours_sum, 1)
            
            # 统计请假次数
            leave_count = 0
            for sheet in sheets:
                remarks = summary_table[name]['备注'][sheet]
                if '请假' in remarks:
                    leave_count += 1
            
            # 将汇总数据添加到summary_table
            summary_table[name]['合计']['汇总'] = total_sum
            summary_table[name]['工时']['汇总'] = hours_sum
            summary_table[name]['平均小时工资']['汇总'] = avg_hourly_wage_sum
            summary_table[name]['备注']['汇总'] = leave_count if leave_count > 0 else ''

        if status_var:
            status_var.set("生成Excel表格...")
        
        # 创建DataFrame以便转为Excel格式
        columns = ['姓名', '类型'] + sheets + ['汇总']

        # 准备行数据
        rows = []
        for name in all_names:
            # 合计行
            total_row = [name, '合计']
            for sheet in sheets + ['汇总']:
                total_row.append(summary_table[name]['合计'].get(sheet, 0))
            rows.append(total_row)
            
            # 工时行
            hours_row = ['', '工时']
            for sheet in sheets + ['汇总']:
                hours_row.append(summary_table[name]['工时'].get(sheet, 0))
            rows.append(hours_row)
            
            # 平均小时工资行
            avg_wage_row = ['', '平均小时工资']
            for sheet in sheets + ['汇总']:
                avg_wage_row.append(summary_table[name]['平均小时工资'].get(sheet, 0))
            rows.append(avg_wage_row)
            
            # 备注行
            remarks_row = ['', '备注']
            for sheet in sheets + ['汇总']:
                remarks_row.append(summary_table[name]['备注'].get(sheet, ''))
            rows.append(remarks_row)
            
            # 添加两行空行（如果不是最后一个人）
            if name != all_names[-1]:
                empty_row1 = [''] * len(columns)
                empty_row2 = [''] * len(columns)
                rows.append(empty_row1)
                rows.append(empty_row2)

        # 创建DataFrame
        df = pd.DataFrame(rows, columns=columns)

        # 保存到Excel
        output_file = os.path.join(desktop_path, os.path.splitext(os.path.basename(excel_file))[0] + '_汇总版.xlsx')
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='汇总表')
            
            # 设置列宽
            worksheet = writer.sheets['汇总表']
            worksheet.column_dimensions['A'].width = 15  # 姓名列
            worksheet.column_dimensions['B'].width = 15  # 类型列
            
            # 日期列
            for i, _ in enumerate(sheets + ['汇总'], start=3):
                col_letter = get_column_letter(i)
                worksheet.column_dimensions[col_letter].width = 10
            
            # 定义样式
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            center_alignment = Alignment(horizontal='center', vertical='center')
            bold_font = Font(bold=True)
            header_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
            
            # 设置表头样式
            for cell in worksheet[1]:
                cell.font = bold_font
                cell.border = thin_border
                cell.alignment = center_alignment
                cell.fill = header_fill
            
            # 合并姓名单元格并设置样式
            current_row = 2  # 从第2行开始（第1行是标题）
            for i, name in enumerate(all_names):
                # 每个人占4行，合并姓名单元格
                worksheet.merge_cells(f'A{current_row}:A{current_row+3}')
                merged_cell = worksheet[f'A{current_row}']
                merged_cell.alignment = Alignment(horizontal='center', vertical='center')
                merged_cell.font = bold_font
                
                # 为每个人的所有单元格添加边框和居中对齐
                for row in range(current_row, current_row + 4):
                    for col in range(1, len(columns) + 1):
                        cell = worksheet.cell(row=row, column=col)
                        cell.border = thin_border
                        if col > 1:  # 除了姓名列之外的所有单元格
                            cell.alignment = center_alignment
                
                # 计算下一个人的起始行（加上4行数据和2行空行）
                if i < len(all_names) - 1:  # 如果不是最后一个人
                    current_row += 6  # 4行数据 + 2行空行
                else:
                    current_row += 4  # 最后一个人不需要加空行

            # 设置冻结窗格
            worksheet.freeze_panes = 'C2'

        # 关闭工作簿
        wb.close()
        
        if status_var:
            status_var.set(f"汇总完成！文件已保存到: {output_file}")
        
        return True
    
    except Exception as e:
        if status_var:
            status_var.set(f"处理过程中出错: {str(e)}")
        return False

# GUI界面
class ExcelProcessorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("成型机工资汇总工具")
        self.root.geometry("600x300")
        self.root.resizable(True, True)
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", font=("Microsoft YaHei UI", 10))
        self.style.configure("TLabel", font=("Microsoft YaHei UI", 10))
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill="both", expand=True)
        
        # 文件选择区域
        file_frame = ttk.Frame(main_frame)
        file_frame.pack(fill="x", pady=10)
        
        ttk.Label(file_frame, text="Excel文件:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        
        self.file_path_var = tk.StringVar()
        file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, width=50)
        file_entry.grid(row=0, column=1, padx=5, pady=5, sticky="we")
        
        browse_button = ttk.Button(file_frame, text="浏览...", command=self.browse_file)
        browse_button.grid(row=0, column=2, padx=5, pady=5)
        
        # 进度条
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill="x", pady=10)
        
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, length=100, mode="determinate")
        progress_bar.pack(fill="x", padx=5, pady=5)
        
        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill="x", pady=10)
        
        self.status_var = tk.StringVar()
        self.status_var.set("请选择Excel文件，然后点击'运行'按钮")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, wraplength=550)
        status_label.pack(fill="x", padx=5, pady=5)
        
        # 运行按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=20)
        
        run_button = ttk.Button(button_frame, text="运行", command=self.run_processing, width=15)
        run_button.pack(side="right", padx=5)
        
        # 帮助按钮
        help_button = ttk.Button(button_frame, text="使用说明", command=self.show_help, width=15)
        help_button.pack(side="left", padx=5)
    
    def browse_file(self):
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)
            self.status_var.set(f"已选择文件: {os.path.basename(file_path)}")
    
    def run_processing(self):
        file_path = self.file_path_var.get()
        if not file_path:
            messagebox.showerror("错误", "请先选择Excel文件")
            return
        
        # 重置进度条
        self.progress_var.set(0)
        
        # 在单独的线程中运行处理，避免界面卡死
        threading.Thread(target=self._run_processing_thread, args=(file_path,), daemon=True).start()
    
    def _run_processing_thread(self, file_path):
        success = process_excel(file_path, self.progress_var, self.status_var)
        if success:
            self.root.after(0, lambda: messagebox.showinfo("成功", f"处理完成！\n汇总文件已保存到桌面。"))
    
    def show_help(self):
        help_text = """使用说明：

1. 点击"浏览"按钮选择Excel文件
2. 点击"运行"按钮开始处理
3. 等待处理完成，汇总结果将保存到桌面

注意事项：
- Excel文件中的sheet页必须是"数字.数字"格式（如4.1、5.2等）
- 每个sheet页必须包含姓名、合计、工时和备注列
- 处理完成后的文件将保存为"原文件名_汇总版.xlsx"
"""
        messagebox.showinfo("使用说明", help_text)

# 如果直接运行此脚本，则启动GUI
if __name__ == "__main__":
    root = tk.Tk()
    app = ExcelProcessorApp(root)
    root.mainloop() 